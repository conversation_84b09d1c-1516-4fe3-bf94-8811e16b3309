'use client'

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Blog<PERSON>ard, BlogCardSkeleton } from "./blog-card"
import { BlogFilters } from "./blog-filters"
import { BlogPagination } from "./blog-pagination"
import { BlogSearch } from "./blog-search"
import { BlogErrorBoundary, BlogLoadingError, BlogEmptyState, BlogNetworkError } from "./blog-error-boundary"
import { BlogLayout, BlogGrid, MobileOnly, DesktopOnly } from "./blog-layout"
import { Button } from "@/components/ui/button"
import { BlogPostCard, BlogCategory, BlogTag, BlogPostFilters, BlogPostListResponse } from "@/types/blog"
import { Grid, List, SlidersHorizontal, Wifi, WifiOff } from "lucide-react"
import { cn } from "@/lib/utils"

interface BlogListingProps {
  initialData?: BlogPostListResponse
  categories: BlogCategory[]
  tags: BlogTag[]
  showFilters?: boolean
  showSearch?: boolean
  showViewToggle?: boolean
  itemsPerPage?: number
}

export function BlogListing({
  initialData,
  categories,
  tags,
  showFilters = true,
  showSearch = true,
  showViewToggle = true,
  itemsPerPage = 12
}: BlogListingProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [posts, setPosts] = useState<BlogPostCard[]>(initialData?.posts || [])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [networkError, setNetworkError] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  
  const [pagination, setPagination] = useState({
    currentPage: initialData?.currentPage || 1,
    totalPages: initialData?.totalPages || 1,
    total: initialData?.total || 0,
    hasNextPage: initialData?.hasNextPage || false,
    hasPreviousPage: initialData?.hasPreviousPage || false
  })

  const [filters, setFilters] = useState<BlogPostFilters>({
    search: searchParams.get('search') || '',
    categoryId: searchParams.get('category') || '',
    tagIds: searchParams.get('tags')?.split(',').filter(Boolean) || [],
    status: 'published'
  })

  const fetchPosts = async (page = 1, newFilters = filters) => {
    setLoading(true)
    setError(null)
    setNetworkError(false)

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        perPage: itemsPerPage.toString(),
        status: 'published',
        ...(newFilters.search && { search: newFilters.search }),
        ...(newFilters.categoryId && { categoryId: newFilters.categoryId }),
        ...(newFilters.tagIds && newFilters.tagIds.length > 0 && { tagIds: newFilters.tagIds.join(',') }),
      })

      const response = await fetch(`/api/blog?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        setPosts(result.data.posts)
        setPagination({
          currentPage: result.data.currentPage,
          totalPages: result.data.totalPages,
          total: result.data.total,
          hasNextPage: result.data.hasNextPage,
          hasPreviousPage: result.data.hasPreviousPage
        })

        // Update URL without causing navigation
        const newParams = new URLSearchParams()
        if (newFilters.search) newParams.set('search', newFilters.search)
        if (newFilters.categoryId) newParams.set('category', newFilters.categoryId)
        if (newFilters.tagIds && newFilters.tagIds.length > 0) newParams.set('tags', newFilters.tagIds.join(','))
        if (page > 1) newParams.set('page', page.toString())

        const newUrl = `${window.location.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`
        window.history.replaceState({}, '', newUrl)
      } else {
        setError(result.error || 'Failed to load blog posts')
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setNetworkError(true)
      } else {
        setError('Failed to load blog posts. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: BlogPostFilters) => {
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handlePageChange = (page: number) => {
    fetchPosts(page)
    // Scroll to top of blog listing
    document.getElementById('blog-listing')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSearch = (searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm }
    handleFiltersChange(newFilters)
  }

  useEffect(() => {
    if (!initialData) {
      fetchPosts()
    }
  }, [])

  return (
    <div id="blog-listing" className="space-y-8">
      {/* Header with Search and View Toggle */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        {/* Search */}
        {showSearch && (
          <div className="flex-1 max-w-md">
            <BlogSearch
              value={filters.search || ''}
              onChange={handleSearch}
              placeholder="Search blog posts..."
            />
          </div>
        )}

        {/* View Controls */}
        <div className="flex items-center space-x-4">
          {/* Mobile Filters Toggle */}
          {showFilters && (
            <Button
              variant="outline"
              size="sm"
              className="lg:hidden"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
            >
              <SlidersHorizontal className="mr-2 h-4 w-4" />
              Filters
            </Button>
          )}

          {/* View Mode Toggle */}
          {showViewToggle && (
            <div className="flex items-center rounded-md border p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-8 px-3"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-8 px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Filters and Content */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
        {/* Sidebar Filters */}
        {showFilters && (
          <div className={cn(
            "lg:col-span-1",
            showMobileFilters ? "block" : "hidden lg:block"
          )}>
            <BlogFilters
              categories={categories}
              tags={tags}
              selectedFilters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={cn(
          showFilters ? "lg:col-span-3" : "lg:col-span-4"
        )}>
          {/* Results Count */}
          <div className="mb-6 flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {loading ? (
                "Loading..."
              ) : (
                `Showing ${posts.length} of ${pagination.total} posts`
              )}
            </p>
          </div>

          {/* Posts Grid/List */}
          {loading ? (
            <div className={cn(
              viewMode === 'grid' 
                ? "grid gap-6 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3"
                : "space-y-6"
            )}>
              {Array.from({ length: itemsPerPage }).map((_, index) => (
                <BlogCardSkeleton 
                  key={index} 
                  variant={viewMode === 'list' ? 'compact' : 'default'} 
                />
              ))}
            </div>
          ) : posts.length > 0 ? (
            <div className={cn(
              viewMode === 'grid' 
                ? "grid gap-6 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3"
                : "space-y-6"
            )}>
              {posts.map((post, index) => (
                <BlogCard
                  key={post.id}
                  post={post}
                  variant={
                    viewMode === 'list' 
                      ? 'compact' 
                      : index === 0 && viewMode === 'grid' 
                        ? 'featured' 
                        : 'default'
                  }
                  showTags={viewMode === 'grid'}
                  className={viewMode === 'list' ? "flex" : undefined}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto max-w-md">
                <h3 className="text-lg font-semibold">No posts found</h3>
                <p className="text-muted-foreground mt-2">
                  {filters.search || filters.categoryId || (filters.tagIds && filters.tagIds.length > 0)
                    ? "Try adjusting your search criteria or filters."
                    : "No blog posts have been published yet."}
                </p>
                {(filters.search || filters.categoryId || (filters.tagIds && filters.tagIds.length > 0)) && (
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => handleFiltersChange({
                      search: '',
                      categoryId: '',
                      tagIds: [],
                      status: 'published'
                    })}
                  >
                    Clear filters
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="mt-12">
              <BlogPagination
                currentPage={pagination.currentPage}
                totalPages={pagination.totalPages}
                hasNextPage={pagination.hasNextPage}
                hasPreviousPage={pagination.hasPreviousPage}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
