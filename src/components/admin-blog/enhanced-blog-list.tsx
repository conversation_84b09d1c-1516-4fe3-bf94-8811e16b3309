'use client'

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { FileText, Pencil, Trash2, Search, ChevronDown, X, Plus, Filter, MoreHorizontal, Eye } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import { BlogPost, BlogCategory, BlogPostFilters, BlogPostListResponse } from "@/types/blog"
import { PostStatus } from "@prisma/client"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"

interface EnhancedBlogListProps {
  initialData?: BlogPostListResponse
  categories: BlogCategory[]
  onRefresh?: () => void
}

export function EnhancedBlogList({ initialData, categories, onRefresh }: EnhancedBlogListProps) {
  const { toast } = useToast()
  const [posts, setPosts] = useState<BlogPost[]>(initialData?.posts || [])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    currentPage: initialData?.currentPage || 1,
    totalPages: initialData?.totalPages || 1,
    total: initialData?.total || 0,
    hasNextPage: initialData?.hasNextPage || false,
    hasPreviousPage: initialData?.hasPreviousPage || false
  })
  
  const [filters, setFilters] = useState<BlogPostFilters>({
    status: 'all',
    search: '',
    categoryId: '',
    dateFrom: undefined,
    dateTo: undefined
  })
  
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [postToDelete, setPostToDelete] = useState<string | null>(null)

  const fetchPosts = async (page = 1, newFilters = filters) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        perPage: '10',
        includeUnpublished: 'true',
        includeDrafts: 'true',
        ...(newFilters.status !== 'all' && { status: newFilters.status }),
        ...(newFilters.search && { search: newFilters.search }),
        ...(newFilters.categoryId && { categoryId: newFilters.categoryId }),
        ...(selectedCategories.length > 0 && { categoryId: selectedCategories[0] }),
      })

      const response = await fetch(`/api/blog?${params}`)
      const result = await response.json()

      if (result.success) {
        setPosts(result.data.posts)
        setPagination({
          currentPage: result.data.currentPage,
          totalPages: result.data.totalPages,
          total: result.data.total,
          hasNextPage: result.data.hasNextPage,
          hasPreviousPage: result.data.hasPreviousPage
        })
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch blog posts",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
      toast({
        title: "Error",
        description: "Failed to fetch blog posts",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handleStatusFilter = (status: string) => {
    const newFilters = { ...filters, status: status as any }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const toggleCategory = (categoryId: string) => {
    const newCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(c => c !== categoryId)
      : [...selectedCategories, categoryId]
    
    setSelectedCategories(newCategories)
    const newFilters = { ...filters, categoryId: newCategories[0] || '' }
    setFilters(newFilters)
    fetchPosts(1, newFilters)
  }

  const handleDeletePost = async (postId: string) => {
    try {
      const response = await fetch(`/api/blog/${postId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: "Blog post deleted successfully",
        })
        fetchPosts(pagination.currentPage)
        onRefresh?.()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete blog post",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error deleting post:', error)
      toast({
        title: "Error",
        description: "Failed to delete blog post",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: PostStatus) => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return <Badge variant="default">Published</Badge>
      case PostStatus.DRAFT:
        return <Badge variant="outline">Draft</Badge>
      case PostStatus.SCHEDULED:
        return <Badge variant="secondary">Scheduled</Badge>
      case PostStatus.ARCHIVED:
        return <Badge variant="destructive">Archived</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handlePageChange = (page: number) => {
    fetchPosts(page)
  }

  useEffect(() => {
    if (!initialData) {
      fetchPosts()
    }
  }, [])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Blog Posts</h2>
          <p className="text-muted-foreground">
            Manage your blog posts, categories, and content
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link href="/admin/blog/categories">
              <Filter className="mr-2 h-4 w-4" />
              Manage Categories
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/blog/new">
              <Plus className="mr-2 h-4 w-4" />
              New Post
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search posts by title, content, or excerpt..."
              className="pl-9"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
        
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>Categories</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              {categories.map((category) => (
                <DropdownMenuCheckboxItem
                  key={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={() => toggleCategory(category.id)}
                >
                  {category.name}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <span>
                  {filters.status === 'all' && "All Posts"}
                  {filters.status === PostStatus.PUBLISHED && "Published"}
                  {filters.status === PostStatus.DRAFT && "Drafts"}
                  {filters.status === PostStatus.SCHEDULED && "Scheduled"}
                  {filters.status === PostStatus.ARCHIVED && "Archived"}
                </span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuCheckboxItem
                checked={filters.status === 'all'}
                onCheckedChange={() => handleStatusFilter('all')}
              >
                All Posts
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.PUBLISHED}
                onCheckedChange={() => handleStatusFilter(PostStatus.PUBLISHED)}
              >
                Published
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.DRAFT}
                onCheckedChange={() => handleStatusFilter(PostStatus.DRAFT)}
              >
                Drafts
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.status === PostStatus.SCHEDULED}
                onCheckedChange={() => handleStatusFilter(PostStatus.SCHEDULED)}
              >
                Scheduled
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Active Filters */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(categoryId => {
            const category = categories.find(c => c.id === categoryId)
            return category ? (
              <Badge key={categoryId} variant="secondary" className="gap-1">
                {category.name}
                <button 
                  onClick={() => toggleCategory(categoryId)}
                  className="ml-1 rounded-full hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ) : null
          })}
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 px-2 text-xs"
            onClick={() => {
              setSelectedCategories([])
              const newFilters = { ...filters, categoryId: '' }
              setFilters(newFilters)
              fetchPosts(1, newFilters)
            }}
          >
            Clear all
          </Button>
        </div>
      )}
