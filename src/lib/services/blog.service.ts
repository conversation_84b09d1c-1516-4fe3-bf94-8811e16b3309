import { prisma } from '@/lib/prisma'
import { 
  BlogPost, 
  BlogCategory, 
  BlogTag, 
  CreateBlogPostData, 
  UpdateBlogPostData,
  CreateCategoryData,
  CreateTagData,
  BlogPostQueryOptions,
  BlogPostListResponse,
  BlogPostFilters,
  AdminBlogStats,
  BlogAnalytics
} from '@/types/blog'
import { PostStatus, Prisma } from '@prisma/client'
import { z } from 'zod'

// Validation schemas
export const createPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  slug: z.string().min(1, 'Slug is required').max(200, 'Slug too long'),
  excerpt: z.string().optional(),
  content: z.string().optional(),
  featuredImage: z.string().url().optional().or(z.literal('')),
  status: z.nativeEnum(PostStatus).default(PostStatus.DRAFT),
  publishedAt: z.string().datetime().optional().nullable(),
  scheduledAt: z.string().datetime().optional().nullable(),
  categoryId: z.string().optional().nullable(),
  tags: z.array(z.string()).default([]),
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional(),
  seoKeywords: z.array(z.string()).optional()
})

export const updatePostSchema = createPostSchema.partial().extend({
  id: z.string()
})

export const createCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug too long'),
  description: z.string().optional(),
  color: z.string().optional(),
  image: z.string().url().optional(),
  parentId: z.string().optional()
})

export const createTagSchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  slug: z.string().min(1, 'Slug is required').max(50, 'Slug too long'),
  color: z.string().optional()
})

export class BlogService {
  /**
   * Get all blog posts with filtering and pagination
   */
  static async getPosts(options: BlogPostQueryOptions = {}): Promise<BlogPostListResponse> {
    const {
      page = 1,
      perPage = 10,
      orderBy = 'createdAt',
      order = 'desc',
      status,
      categoryId,
      tagIds,
      authorId,
      search,
      dateFrom,
      dateTo,
      includeUnpublished = false,
      includeDrafts = false
    } = options

    const skip = (page - 1) * perPage
    const take = perPage

    // Build where clause
    const where: Prisma.PostWhereInput = {}

    // Status filter
    if (status && status !== 'all') {
      where.status = status as PostStatus
    } else if (!includeUnpublished && !includeDrafts) {
      where.status = PostStatus.PUBLISHED
    }

    // Category filter
    if (categoryId) {
      where.categoryId = categoryId
    }

    // Tags filter
    if (tagIds && tagIds.length > 0) {
      where.tags = {
        some: {
          id: {
            in: tagIds
          }
        }
      }
    }

    // Author filter
    if (authorId) {
      where.authorId = authorId
    }

    // Search filter
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Date range filter
    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom)
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo)
      }
    }

    // Execute query
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        skip,
        take,
        orderBy: { [orderBy]: order },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true,
              description: true
            }
          },
          tags: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true
            }
          },
          _count: {
            select: {
              comments: true,
              tags: true
            }
          }
        }
      }),
      prisma.post.count({ where })
    ])

    const totalPages = Math.ceil(total / perPage)

    return {
      posts: posts as BlogPost[],
      total,
      totalPages,
      currentPage: page,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  }

  /**
   * Get a single blog post by ID
   */
  static async getPostById(id: string, includeUnpublished = false): Promise<BlogPost | null> {
    const where: Prisma.PostWhereInput = { id }
    
    if (!includeUnpublished) {
      where.status = PostStatus.PUBLISHED
    }

    const post = await prisma.post.findFirst({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
            description: true
          }
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        comments: {
          where: { approved: true },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    image: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        blocks: {
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            comments: true,
            tags: true
          }
        }
      }
    })

    return post as BlogPost | null
  }

  /**
   * Get a single blog post by slug
   */
  static async getPostBySlug(slug: string, includeUnpublished = false): Promise<BlogPost | null> {
    const where: Prisma.PostWhereInput = { slug }
    
    if (!includeUnpublished) {
      where.status = PostStatus.PUBLISHED
    }

    const post = await prisma.post.findFirst({
      where,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
            description: true
          }
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        comments: {
          where: { approved: true },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    image: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        blocks: {
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            comments: true,
            tags: true
          }
        }
      }
    })

    return post as BlogPost | null
  }

  /**
   * Create a new blog post
   */
  static async createPost(data: CreateBlogPostData, authorId: string): Promise<BlogPost> {
    const validatedData = createPostSchema.parse(data)
    
    // Check if slug already exists
    const existingPost = await prisma.post.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingPost) {
      throw new Error('A post with this slug already exists')
    }

    // Calculate read time if content is provided
    let readTime = 1
    if (validatedData.content) {
      const wordsPerMinute = 200
      const words = validatedData.content.replace(/<[^>]*>/g, '').split(/\s+/).length
      readTime = Math.ceil(words / wordsPerMinute) || 1
    }

    const post = await prisma.post.create({
      data: {
        ...validatedData,
        readTime,
        authorId,
        tags: validatedData.tags ? {
          connect: validatedData.tags.map(tagId => ({ id: tagId }))
        } : undefined
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
            description: true
          }
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: true,
            tags: true
          }
        }
      }
    })

    return post as BlogPost
  }

  /**
   * Update an existing blog post
   */
  static async updatePost(data: UpdateBlogPostData): Promise<BlogPost> {
    const validatedData = updatePostSchema.parse(data)
    const { id, tags, ...updateData } = validatedData

    // Check if post exists
    const existingPost = await prisma.post.findUnique({
      where: { id }
    })

    if (!existingPost) {
      throw new Error('Post not found')
    }

    // Check if slug already exists (if being updated)
    if (updateData.slug && updateData.slug !== existingPost.slug) {
      const slugExists = await prisma.post.findUnique({
        where: { slug: updateData.slug }
      })

      if (slugExists) {
        throw new Error('A post with this slug already exists')
      }
    }

    // Calculate read time if content is being updated
    if (updateData.content) {
      const wordsPerMinute = 200
      const words = updateData.content.replace(/<[^>]*>/g, '').split(/\s+/).length
      updateData.readTime = Math.ceil(words / wordsPerMinute) || 1
    }

    const post = await prisma.post.update({
      where: { id },
      data: {
        ...updateData,
        tags: tags ? {
          set: tags.map(tagId => ({ id: tagId }))
        } : undefined
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true,
            description: true
          }
        },
        tags: {
          select: {
            id: true,
            name: true,
            slug: true,
            color: true
          }
        },
        _count: {
          select: {
            comments: true,
            tags: true
          }
        }
      }
    })

    return post as BlogPost
  }

  /**
   * Delete a blog post
   */
  static async deletePost(id: string): Promise<void> {
    const post = await prisma.post.findUnique({
      where: { id }
    })

    if (!post) {
      throw new Error('Post not found')
    }

    await prisma.post.delete({
      where: { id }
    })
  }

  /**
   * Get all categories
   */
  static async getCategories(): Promise<BlogCategory[]> {
    const categories = await prisma.category.findMany({
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            posts: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return categories as BlogCategory[]
  }

  /**
   * Create a new category
   */
  static async createCategory(data: CreateCategoryData, createdBy: string): Promise<BlogCategory> {
    const validatedData = createCategorySchema.parse(data)

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingCategory) {
      throw new Error('A category with this slug already exists')
    }

    const category = await prisma.category.create({
      data: {
        ...validatedData,
        createdBy
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        parent: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        children: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        _count: {
          select: {
            posts: true
          }
        }
      }
    })

    return category as BlogCategory
  }

  /**
   * Get all tags
   */
  static async getTags(): Promise<BlogTag[]> {
    const tags = await prisma.tag.findMany({
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        _count: {
          select: {
            posts: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return tags as BlogTag[]
  }

  /**
   * Create a new tag
   */
  static async createTag(data: CreateTagData, createdBy: string): Promise<BlogTag> {
    const validatedData = createTagSchema.parse(data)

    // Check if slug already exists
    const existingTag = await prisma.tag.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingTag) {
      throw new Error('A tag with this slug already exists')
    }

    const tag = await prisma.tag.create({
      data: {
        ...validatedData,
        createdBy
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true
          }
        },
        _count: {
          select: {
            posts: true
          }
        }
      }
    })

    return tag as BlogTag
  }

  /**
   * Get admin statistics
   */
  static async getAdminStats(): Promise<AdminBlogStats> {
    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      scheduledPosts,
      totalCategories,
      totalTags,
      totalComments,
      pendingComments
    ] = await Promise.all([
      prisma.post.count(),
      prisma.post.count({ where: { status: PostStatus.PUBLISHED } }),
      prisma.post.count({ where: { status: PostStatus.DRAFT } }),
      prisma.post.count({ where: { status: PostStatus.SCHEDULED } }),
      prisma.category.count(),
      prisma.tag.count(),
      prisma.comment.count(),
      prisma.comment.count({ where: { approved: false } })
    ])

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      scheduledPosts,
      totalCategories,
      totalTags,
      totalComments,
      pendingComments
    }
  }

  /**
   * Get blog analytics
   */
  static async getAnalytics(): Promise<BlogAnalytics> {
    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      totalComments,
      popularPosts,
      recentPosts,
      topCategories,
      topTags
    ] = await Promise.all([
      prisma.post.count(),
      prisma.post.count({ where: { status: PostStatus.PUBLISHED } }),
      prisma.post.count({ where: { status: PostStatus.DRAFT } }),
      prisma.comment.count(),
      // Popular posts (by view count - you'll need to implement view tracking)
      prisma.post.findMany({
        where: { status: PostStatus.PUBLISHED },
        take: 5,
        orderBy: { createdAt: 'desc' }, // Replace with viewCount when implemented
        select: {
          id: true,
          title: true,
          slug: true,
          excerpt: true,
          featuredImage: true,
          publishedAt: true,
          readTime: true,
          author: {
            select: {
              name: true,
              image: true
            }
          },
          category: {
            select: {
              name: true,
              slug: true,
              color: true
            }
          },
          tags: {
            select: {
              name: true,
              slug: true,
              color: true
            }
          }
        }
      }),
      // Recent posts
      prisma.post.findMany({
        where: { status: PostStatus.PUBLISHED },
        take: 5,
        orderBy: { publishedAt: 'desc' },
        select: {
          id: true,
          title: true,
          slug: true,
          excerpt: true,
          featuredImage: true,
          publishedAt: true,
          readTime: true,
          author: {
            select: {
              name: true,
              image: true
            }
          },
          category: {
            select: {
              name: true,
              slug: true,
              color: true
            }
          },
          tags: {
            select: {
              name: true,
              slug: true,
              color: true
            }
          }
        }
      }),
      // Top categories
      prisma.category.findMany({
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          _count: {
            select: {
              posts: true
            }
          }
        },
        orderBy: {
          posts: {
            _count: 'desc'
          }
        },
        take: 10
      }),
      // Top tags
      prisma.tag.findMany({
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          _count: {
            select: {
              posts: true
            }
          }
        },
        orderBy: {
          posts: {
            _count: 'desc'
          }
        },
        take: 10
      })
    ])

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      totalViews: 0, // Implement view tracking
      totalComments,
      popularPosts: popularPosts as any,
      recentPosts: recentPosts as any,
      topCategories: topCategories.map(cat => ({
        ...cat,
        postCount: cat._count.posts
      })) as any,
      topTags: topTags.map(tag => ({
        ...tag,
        postCount: tag._count.posts
      })) as any
    }
  }

  /**
   * Search posts
   */
  static async searchPosts(query: string, limit = 10) {
    const posts = await prisma.post.findMany({
      where: {
        status: PostStatus.PUBLISHED,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { excerpt: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } }
        ]
      },
      take: limit,
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featuredImage: true,
        publishedAt: true,
        readTime: true,
        author: {
          select: {
            name: true,
            image: true
          }
        },
        category: {
          select: {
            name: true,
            slug: true,
            color: true
          }
        },
        tags: {
          select: {
            name: true,
            slug: true,
            color: true
          }
        }
      },
      orderBy: { publishedAt: 'desc' }
    })

    return posts
  }

  /**
   * Get related posts
   */
  static async getRelatedPosts(postId: string, limit = 3) {
    const post = await prisma.post.findUnique({
      where: { id: postId },
      select: {
        categoryId: true,
        tags: {
          select: { id: true }
        }
      }
    })

    if (!post) return []

    const tagIds = post.tags.map(tag => tag.id)

    const relatedPosts = await prisma.post.findMany({
      where: {
        id: { not: postId },
        status: PostStatus.PUBLISHED,
        OR: [
          { categoryId: post.categoryId },
          {
            tags: {
              some: {
                id: {
                  in: tagIds
                }
              }
            }
          }
        ]
      },
      take: limit,
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        featuredImage: true,
        publishedAt: true,
        readTime: true,
        author: {
          select: {
            name: true,
            image: true
          }
        },
        category: {
          select: {
            name: true,
            slug: true,
            color: true
          }
        },
        tags: {
          select: {
            name: true,
            slug: true,
            color: true
          }
        }
      },
      orderBy: { publishedAt: 'desc' }
    })

    return relatedPosts
  }

  /**
   * Generate slug from title
   */
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
      .trim()
  }

  /**
   * Calculate read time from content
   */
  static calculateReadTime(content: string): number {
    const wordsPerMinute = 200
    const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length
    return Math.ceil(words / wordsPerMinute) || 1
  }
}
